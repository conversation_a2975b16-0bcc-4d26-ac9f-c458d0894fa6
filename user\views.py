import aiohttp
import asyncio
from django.shortcuts import render
import json
import random
import string
import uuid
import jwt
import base64
import secrets
import time
import hashlib
import urllib.parse
from decimal import Decimal
from datetime import timedelta
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST, require_http_methods
from django.contrib.auth import authenticate
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
import os
from django.shortcuts import redirect
from functools import wraps
import requests

from .models import User, EmailVerificationCode
from api.models import Category, Goods, PriceTemplate, MemberLevel, PaymentMethod, Coupon, Order
from api.views import update_docking_good_info, get_md5
from django.db import transaction
from utils.template_router import smart_render

JWT_SECRET_KEY = settings.JWT_SECRET_KEY
JWT_ALGORITHM = settings.JWT_ALGORITHM

# 获取动态模板路径的内部函数
def get_template_path(templates_path: str) -> str:

    templates_path = templates_path[:-5]
    try:
        # 读取static文件夹下的data.json文件
        data_file_path = os.path.join('static', 'data.json')

        # 如果文件不存在，创建默认配置
        if not os.path.exists(data_file_path):
            os.makedirs('static', exist_ok=True)
            default_config = {"forestage_templates": "default"}
            with open(data_file_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)

        # 读取配置文件
        with open(data_file_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 获取forestage_templates字段值
        forestage_templates = config.get('forestage_templates', 'default')

        if forestage_templates == 'caihong' and templates_path == 'category':
            return 'vue/pages/index.vue'

        # 根据配置返回相应的模板路径
        if forestage_templates == 'default':
            return f'user/{templates_path}.html'
        else:
            return f'pages/forestage_templates_for_{forestage_templates}/{templates_path}.vue'

    except Exception as e:
        return f'user/{templates_path}'

# 自定义登录认证装饰器
def login_required(view_func):

    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # 尝试从请求头获取Token
        token = request.headers.get('Token')
        
        # 如果请求头中没有Token，尝试从Cookie中获取
        if not token and 'token' in request.COOKIES:
            token = request.COOKIES.get('token')
            
        # 如果还是没有找到Token，尝试从GET或POST参数中获取
        if not token:
            token = request.GET.get('token') or request.POST.get('token')
        
        # 如果找到了Token，验证它
        if token:
            is_valid, result = verify_token(token, request)
            
            if is_valid:
                # Token有效，允许访问视图
                # 将用户对象添加到请求中，以便视图函数使用
                request.user = result
                return view_func(request, *args, **kwargs)
        
        # 没有找到有效的Token，重定向到登录页面
        return redirect('user:login_page')
    
    return wrapper

# Create your views here.

# 用户登录页面
def user_login_page(request):
    return smart_render(request, get_template_path('user_logon.html'), {})

# 用户个人中心页面
@login_required
def profile_page(request):
    import json
    from datetime import datetime, timedelta

    # 准备用户数据以传递给模板
    user_data = {
        'id': str(request.user.user_id),
        'username': request.user.username,
        'email': request.user.email,
        'user_key': str(request.user.user_key),
        'date_joined': request.user.date_joined.isoformat()
    }

    # 订单状态映射关系
    status_mapping = {
        'failed': '失败',
        'pending': '待付款',
        'processing': '处理中',
        'completed': '已完成',
        'canceled': '已取消',
        'refunded': '已退款'
    }

    # 查询用户订单数据
    orders = []
    try:
        # 通过userId匹配Order模型user字段
        user_orders = Order.objects.filter(user=str(request.user.user_id)).order_by('-created_at')

        for order in user_orders:
            try:
                # 解析订单数据JSON
                order_data = order.data

                # 转换UTC时间为东八区时间
                if order.created_at:
                    # 将UTC时间转换为东八区时间（UTC+8）
                    beijing_time = order.created_at + timedelta(hours=8)
                    create_time_str = beijing_time.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    create_time_str = ''

                # 提取订单信息
                order_info = {
                    'id': order.id,  # 订单号
                    'product_name': order_data.get('product', {}).get('name', '未知商品'),  # 商品名称
                    'final_price': order_data.get('price', {}).get('final_price', '0.00'),  # 订单价格
                    'status': order_data.get('status', 'unknown'),  # 原始状态
                    'status_text': status_mapping.get(order_data.get('status', 'unknown'), '未知状态'),  # 映射后的状态
                    'create_time': create_time_str  # 东八区时间
                }

                orders.append(order_info)

            except (json.JSONDecodeError, KeyError, AttributeError) as e:
                # 如果某个订单数据解析失败，跳过该订单
                continue

    except Exception as e:
        # 如果查询订单失败，设置为空列表
        orders = []

    # 将订单数据转换为JSON格式，供前端JavaScript使用
    orders_json = json.dumps(orders, ensure_ascii=False)

    # 渲染页面并传递用户数据和订单数据
    return smart_render(request, get_template_path('profile.html'), {
        'user_data': user_data,
        'orders': orders,
        'orders_json': orders_json
    })

# 商品分类页面
def category_page(request):
    return smart_render(request, get_template_path('category.html'), {})

# 商品列表页面
def product_list_page(request):
    return smart_render(request, get_template_path('product_list.html'), {})


# 订单失败页面
def order_failed_page(request):
    """
    订单生成失败页面视图

    参数：
    - id: 商品ID，用于返回确认订单页面

    说明：
    1. 显示订单生成失败的提示信息
    2. 提供返回确认订单页面和首页的按钮
    3. 支持传递商品ID参数
    """
    # 获取商品ID参数（可选）
    product_id = request.GET.get('id', '')

    # 准备传递给模板的上下文数据
    context = {
        'product_id': product_id,
        'page_title': '订单生成失败'
    }

    return smart_render(request, get_template_path('order-failed.html'), context)


def payment_qrcode_page(request):
    """
    支付二维码显示页面
    GET /payment/qrcode/?order={order_id}
    """
    try:
        # 获取订单ID参数
        order_id = request.GET.get('order')
        if not order_id:
            context = {
                'error_title': '参数错误',
                'error_message': '缺少订单号参数，请检查链接是否正确。',
                'error_icon': 'fa-exclamation-triangle'
            }
            return smart_render(request, get_template_path('product-error.html'), context)

        # 从订单表获取订单数据
        try:
            from api.models import Order
            order = Order.objects.get(id=order_id)
            order_data = order.data
        except Order.DoesNotExist:
            context = {
                'error_title': '订单不存在',
                'error_message': '订单不存在，请重新下单。',
                'error_icon': 'fa-exclamation-circle'
            }
            return smart_render(request, get_template_path('product-error.html'), context)

        # 检查订单状态
        if order_data.get('status') != 'pending':
            context = {
                'error_title': '订单状态异常',
                'error_message': '订单状态异常，无法显示支付信息。',
                'error_icon': 'fa-exclamation-triangle'
            }
            return smart_render(request, get_template_path('product-error.html'), context)

        # 获取支付信息
        payment_info = order_data.get('payment', {})
        payment_type = payment_info.get('type')


        # 根据支付类型获取二维码URL
        qrcode_url = None
        if payment_type == 'api':
            # 从订单数据中获取二维码URL
            try:
               qrcode_url = payment_info.get('url')
            except Exception as e:
                pass

        if not qrcode_url:
            context = {
                'error_title': '支付信息获取失败',
                'error_message': '无法获取支付二维码，请重新下单或联系客服。',
                'error_icon': 'fa-exclamation-triangle'
            }
            return smart_render(request, get_template_path('product-error.html'), context)

        # 准备模板上下文
        context = {
            'order_id': order_id,
            'order_data': order_data,
            'qrcode_url': qrcode_url,
            'product_name': order_data.get('product', {}).get('name', '商品'),
            'final_price': order_data.get('price', {}).get('final_price', '0.00'),
            'payment_method_name': payment_info.get('name', '支付方式'),
            'created_time': order.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }

        return smart_render(request, get_template_path('payment-qrcode.html'), context)

    except Exception as e:
        context = {
            'error_title': '页面加载失败',
            'error_message': '页面加载时发生错误，请稍后重试。如果问题持续存在，请联系客服。',
            'error_icon': 'fa-exclamation-triangle'
        }
        return smart_render(request, get_template_path('product-error.html'), context)


def payment_success_page(request):
    """
    支付成功页面
    GET /payment/success/?order={order_id}
    """
    try:
        # 获取订单ID参数
        order_id = request.GET.get('order')
        if not order_id:
            order_id = request.GET.get('out_trade_no')
        if order_id is None:
            context = {
                'error_title': '参数错误',
                'error_message': '缺少订单号参数，请检查链接是否正确。',
                'error_icon': 'fa-exclamation-triangle'
            }
            return smart_render(request, get_template_path('product-error.html'), context)

        # 从订单表获取订单数据
        try:
            from api.models import Order
            order = Order.objects.get(id=order_id)
            order_data = order.data
        except Order.DoesNotExist:
            context = {
                'error_title': '订单不存在',
                'error_message': '订单不存在。',
                'error_icon': 'fa-exclamation-circle'
            }
            return smart_render(request, get_template_path('product-error.html'), context)

        # 检查订单状态
        current_status = order_data.get('status', 'pending')

        # 获取支付时间，优先使用实际支付完成时间
        payment_info = order_data.get('payment', {})
        pay_time = payment_info.get('pay_time')  # 实际支付完成时间

        if pay_time:
            # 如果存在支付完成时间，使用支付完成时间
            display_time = pay_time
            time_label = '支付时间'
        else:
            # 如果没有支付完成时间，使用订单创建时间
            display_time = order.created_at.strftime('%Y-%m-%d %H:%M:%S')
            time_label = '订单创建时间'

        context = {
                'order_id': order_id,
                'order_data': order_data,
                'product_name': order_data.get('product', {}).get('name', '商品'),
                'final_price': order_data.get('price', {}).get('final_price', '0.00'),
                'created_time': display_time,
                'time_label': time_label,
                'order_status': current_status
            }

        return smart_render(request, get_template_path('payment-success.html'), context)
        

    except Exception as e:
        context = {
            'error_title': '页面加载失败',
            'error_message': '页面加载时发生错误，请稍后重试。如果问题持续存在，请联系客服。',
            'error_icon': 'fa-exclamation-triangle'
        }
        return smart_render(request, get_template_path('product-error.html'), context)


# 商品详情页面
def product_detail_page(request):

    try:
        # 获取商品ID参数
        product_id = request.GET.get('id')

        if not product_id:
            # 商品ID为空，返回自定义错误页面
            context = {
                'error_title': '参数错误',
                'error_message': '商品ID不能为空，请检查访问链接是否正确。',
                'error_icon': 'fa-exclamation-circle'
            }
            return smart_render(request, get_template_path('product-error.html'), context)

        # 获取商品信息
        try:
            goods = Goods.objects.get(id=product_id)
        except Goods.DoesNotExist:
            # 商品不存在，返回自定义错误页面
            context = {
                'error_title': '商品不存在',
                'error_message': f'商品ID "{product_id}" 不存在或已被删除。请检查商品链接是否正确，或浏览其他商品。',
                'error_icon': 'fa-box-open'
            }
            return smart_render(request, get_template_path('product-error.html'), context)

        # 判断是否为对接商品，如果是则更新商品信息
        if goods.type == '3':
            try:
                # 导入异步更新函数
                from api.views import update_docking_good_info_async

                # 使用异步更新单个商品
                async def update_single_good():
                    connector = aiohttp.TCPConnector(limit=10)
                    timeout = aiohttp.ClientTimeout(total=3)

                    async with aiohttp.ClientSession(
                        connector=connector,
                        timeout=timeout
                    ) as session:
                        return await update_docking_good_info_async(session, goods.id)

                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    update_result = loop.run_until_complete(update_single_good())

                    if update_result.get('code') == 200:
                        # 更新成功，重新获取商品信息以确保数据最新
                        goods.refresh_from_db()
                        print(f"商品详情页面：异步更新对接商品 {goods.id} 成功")
                    else:
                        print(f"商品详情页面：异步更新对接商品 {goods.id} 失败: {update_result.get('msg')}")

                finally:
                    loop.close()

            except Exception as e:
                # 更新失败不影响页面显示，只记录错误
                print(f"商品详情页面：更新对接商品 {goods.id} 信息失败: {str(e)}")

        """
        # 获取用户信息和会员等级
        user_membership_key = "NormalUser"  # 默认为普通用户
        user_info = None

        # 尝试从请求头获取Token
        token = request.headers.get('Token')

        # 如果请求头中没有Token，尝试从Cookie中获取
        if not token and 'token' in request.COOKIES:
            token = request.COOKIES.get('token')

        # 如果有Token，验证并获取用户信息
        if token:
            try:
                # 解析Token
                payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
                token_user_id = payload.get('user_id')

                # 检查是否为游客用户
                if payload.get('guest', False):
                    # 游客用户
                    user_membership_key = "NormalUser"
                    user_info = {
                        'type': '游客用户',
                        'id': token_user_id,
                        'user_key': payload.get('user_key'),
                        'guest': True
                    }
                else:
                    # 正式用户，从数据库获取详细信息
                    try:
                        user = User.objects.get(user_id=token_user_id)
                        membership_level_value = user.membership_level

                        # 设置会员等级标识
                        if membership_level_value == "普通用户":
                            user_membership_key = "NormalUser"
                        else:
                            user_membership_key = membership_level_value

                        user_info = {
                            'type': '正式用户',
                            'id': str(user.user_id),
                            'username': user.username,
                            'email': user.email,
                            'user_key': str(user.user_key),
                            'membership_level': membership_level_value
                        }
                    except User.DoesNotExist:
                        # 用户不存在，使用默认等级
                        user_membership_key = "NormalUser"
            except Exception as e:
                # Token解析失败，使用默认等级
                user_membership_key = "NormalUser"

        # 计算实际价格
        try:
            actual_price = calculate_actual_price(goods.price, goods.price_template, user_membership_key)
        except Exception as e:
            # 价格计算异常，使用原价
            actual_price = str(goods.price)

        # 处理商品参数
        attach_data = []
        if goods.attach:
            try:
                attach_json = json.loads(goods.attach)
                if isinstance(attach_json, list):
                    attach_data = attach_json
                elif isinstance(attach_json, dict):
                    # 如果是字典格式，转换为列表格式
                    for key, value in attach_json.items():
                        attach_data.append({
                            'name': key,
                            'tip': str(value)
                        })
            except json.JSONDecodeError:
                # JSON解析失败，保持空列表
                attach_data = []

        # 处理库存状态
        stock_status = "充足"
        stock_class = ""
        if goods.status == "3":  # 售罄
            stock_status = "售罄"
            stock_class = "out"
        elif goods.stock < 10:  # 库存不足
            stock_status = f"库存不足 ({goods.stock})"
            stock_class = "low"
        else:
            stock_status = f"库存充足 ({goods.stock}+)"
            stock_class = ""
        """

        context = {
            'page_title': f'{goods.name} - 商品详情',
            'notice': goods.notice or ''  # 商品弹窗公告内容
        }
        return smart_render(request, get_template_path('product-detail.html'), context)

    except Exception as e:
        # 处理其他异常
        context = {
            'error_title': '页面加载失败',
            'error_message': '页面加载时发生错误，请稍后重试。如果问题持续存在，请联系客服。',
            'error_icon': 'fa-exclamation-triangle'
        }
        return smart_render(request, get_template_path('product-error.html'), context)

# 生成支付链接
def generate_payment_url(payment_method, payment_way, order_id, product_name, amount, device_type='desktop')->dict:

    try:
        # 使用PaymentService生成支付链接
        from utils.shared_services.payment.payment_service import PaymentService
        
        # 创建服务上下文
        context = {'request_type': 'user'}
        
        # 使用支付服务
        payment_service = PaymentService(context)
        
        # 准备订单信息
        order_info = {
            'order_id': order_id,
            'product_name': product_name,
            'amount': amount,
            'payment_way': payment_way
        }
        
        try:
            # 使用服务层生成支付链接
            result = payment_service.generate_payment_url(
                payment_method_id=payment_method.id,
                order_info=order_info,
                device_type=device_type
            )
            
            return {
                'code': 200,
                'msg': '生成支付链接成功',
                'data': {
                    'payment_url': result['payment_url'],
                    'transaction_id': result['transaction_id']
                }
            }
            
        except Exception as service_error:
            # 如果服务层失败，回退到原有逻辑
            return _generate_payment_url_fallback(payment_method, payment_way, order_id, product_name, amount, device_type)

    except Exception as e:
        return {
            'code': 401,
            'msg': '生成支付链接失败'
        }


def _generate_payment_url_fallback(payment_method, payment_way, order_id, product_name, amount, device_type='desktop')->dict:
    """
    支付链接生成的回退逻辑（保持原有实现）
    """
    try:
        # 检查接口类型
        if payment_method.interface_type == "code_e_payment":

            # 解析data配置
            if not payment_method.data_json:
                return {
                    'code': 401,
                    'msg': '生成支付链接失败'
                }

            try:
                config = json.loads(payment_method.data_json)
            except json.JSONDecodeError:
                return {
                    'code': 401,
                    'msg': '生成支付链接失败'
                }

            # 检查api_type
            api_type = config.get('api_type')
            if api_type == "submit.php":
                return generate_epay_url(config, payment_way, order_id, product_name, amount)
            elif api_type == "apisubmit.php":  # baseurl+/pay/apisubmit 这里应该没有.php
                return generate_epay_url_api(config, payment_way, order_id, product_name, amount)
            else:
                # 其他api_type
                return {
                    'code': 401,
                    'msg': '生成支付链接失败'
                }
        elif payment_method.interface_type == "alipay":
            # 支付宝官方支付
            from api.views import AlipayView
            try:
                alipay_view = AlipayView(payment_method.id, order_id, product_name, amount, device_type)
                return alipay_view.generate_payment_url()
            except Exception as e:
                return {
                    'code': 401,
                    'msg': f'生成支付链接失败: {str(e)}'
                }
        else:
            # 其他接口类型
            return {
                'code': 401,
                'msg': '生成支付链接失败'
            }

    except Exception as e:
        return {
            'code': 401,
            'msg': '生成支付链接失败'
        }

def generate_epay_url_api(config, payment_way, order_id, product_name, amount)->dict:

    try:
        # 获取配置参数
        domain = config.get('domain', '')
        appid = config.get('appid', '')
        key = config.get('key', '')

        if not all([domain, appid, key]):
            raise ValueError('支付配置不完整')

        # 构建基础URL
        base_url = domain.rstrip('/') + f'/pay/apisubmit'

        # 构建参数
        params = {
            'pid': appid,
            'type': payment_way,
            'out_trade_no': order_id,
            'notify_url': f'{get_site_base_url()}/api/epay_notify',
            'return_url': f'{get_site_base_url()}/payment/success?order={order_id}',
            'name': product_name,
            'money': str(amount)
        }

        # 生成签名
        sign = generate_epay_sign(params, key)
        params['sign'] = sign
        params['sign_type'] = 'MD5'

        # 构建完整URL
        query_string = urllib.parse.urlencode(params)
        full_url = f'{base_url}?{query_string}'
        
        response = requests.get(full_url)
        if response.status_code == 200 and response.json().get('code') == 200:
            return {
                'code': 200,
                'msg': '生成支付订单成功',
                'type':'api',
                'data':{
                    'money':response.json().get('money'),
                    'type':response.json().get('type'),
                    'out_trade_no':response.json().get('out_trade_no'),
                    'trade_no':response.json().get('trade_no'),
                    'qrcode':response.json().get('qrcode'),
                    'code_url':response.json().get('code_url')
                }
            }
        else:
            return {
                'code': 401,
                'msg': f'生成支付订单失败,{response.json().get("msg")}'
            }

    except Exception as e:
        return {
            'code': 401,
            'msg': '生成支付订单失败'
        }

def generate_epay_url(config, payment_way, order_id, product_name, amount):

    try:
        # 获取配置参数
        domain = config.get('domain', '')
        appid = config.get('appid', '')
        key = config.get('key', '')

        if not all([domain, appid, key]):
            raise ValueError('支付配置不完整')

        # 构建基础URL
        base_url = domain.rstrip('/') + f'/submit.php'

        # 构建参数
        params = {
            'pid': appid,
            'type': payment_way,
            'out_trade_no': order_id,
            'notify_url': f'{get_site_base_url()}/api/epay_notify',
            'return_url': f'{get_site_base_url()}/payment/success?order={order_id}',
            # 支付成功页面渲染视图的时候根据order_id获取订单信息 并渲染订单信息
            'name': product_name,  # URL编码商品名称
            'money': str(amount)
        }

        # 生成签名
        sign = generate_epay_sign(params, key)
        params['sign'] = sign
        params['sign_type'] = 'MD5'

        # 构建完整URL
        query_string = urllib.parse.urlencode(params)
        full_url = f'{base_url}?{query_string}'

        return {
            'code': 200,
            'msg': '生成支付订单成功',
            'type':'url',
            'data':{
                'url':full_url
            }
        }

    except Exception as e:
        return {
            'code': 401,
            'msg': '生成支付订单失败'
        }


def generate_epay_sign(params, key)->str:
    """
    生成易支付签名

    Args:
        params: 参数字典
        key: 签名密钥

    Returns:
        str: MD5签名
    """
    # 排除sign和sign_type参数
    sign_params = {k: v for k, v in params.items() if k not in ['sign', 'sign_type']}

    # 按ASCII码排序
    sorted_params = sorted(sign_params.items())

    # 构建签名字符串
    sign_string = '&'.join([f'{k}={v}' for k, v in sorted_params])
    sign_string += key

    # 生成MD5签名
    return hashlib.md5(sign_string.encode('utf-8')).hexdigest()


def get_site_base_url(request=None) -> str:
    
    import os
    import yaml
    from django.conf import settings
    import logging

    logger = logging.getLogger(__name__)

    try:
        # 方式1: 基于request对象动态生成（优先级最高）
        if request is not None:
            try:
                # 检测协议
                is_secure = request.is_secure()
                if not is_secure:
                    # 检查反向代理头部
                    forwarded_proto = request.META.get('HTTP_X_FORWARDED_PROTO', '').lower()
                    forwarded_ssl = request.META.get('HTTP_X_FORWARDED_SSL', '').lower()
                    is_secure = forwarded_proto == 'https' or forwarded_ssl == 'on'

                protocol = 'https' if is_secure else 'http'
                host = request.get_host()

                # 构建URL
                base_url = f"{protocol}://{host}"
                return base_url

            except Exception as e:
                logger.warning(f"基于request对象生成URL失败: {e}")

        # 方式2: 从deploy_config.yaml配置文件读取
        try:
            config_file = 'deploy_config.yaml'
            if os.path.exists(config_file):
                try:
                    config = yaml.safe_load(open(config_file, 'r', encoding='utf-8'))
                except ImportError:
                    config = None

                if config and 'server' in config:
                    server_config = config['server']
                    domain = server_config.get('domain', '').strip()
                    ip = server_config.get('ip', '').strip()
                    port = server_config.get('port', 8000)

                    # 优先使用域名
                    if domain:
                        # 检测是否需要HTTPS（域名通常使用HTTPS）
                        protocol = 'https' if not domain.startswith(('localhost', '127.0.0.1', '192.168.')) else 'http'

                        # 处理端口
                        if (protocol == 'https' and port != 443) or (protocol == 'http' and port != 80):
                            base_url = f"{protocol}://{domain}:{port}"
                        else:
                            base_url = f"{protocol}://{domain}"

                        return base_url

                    # 使用IP地址
                    elif ip:
                        protocol = 'http'  # IP地址默认使用HTTP

                        # 处理端口
                        if port != 80:
                            base_url = f"{protocol}://{ip}:{port}"
                        else:
                            base_url = f"{protocol}://{ip}"
                        return base_url

        except Exception as e:
            return

        # 方式3: 从环境变量读取
        try:
            # 检查完整的BASE_URL环境变量
            base_url_env = os.getenv('BASE_URL', '').strip()
            if base_url_env:
                return base_url_env

            # 检查分离的环境变量
            domain_env = os.getenv('DOMAIN', '').strip()
            if domain_env:
                protocol = 'https' if not domain_env.startswith(('localhost', '127.0.0.1', '192.168.')) else 'http'
                port_env = os.getenv('PORT', '8000')

                try:
                    port = int(port_env)
                    if (protocol == 'https' and port != 443) or (protocol == 'http' and port != 80):
                        base_url = f"{protocol}://{domain_env}:{port}"
                    else:
                        base_url = f"{protocol}://{domain_env}"
                    return base_url
                except ValueError:
                    return

        except Exception as e:
            logger.warning(f"读取环境变量失败: {e}")

        # 方式4: 从Django settings读取
        try:
            if hasattr(settings, 'ALLOWED_HOSTS') and settings.ALLOWED_HOSTS:
                # 从ALLOWED_HOSTS中找到第一个有效的域名
                for host in settings.ALLOWED_HOSTS:
                    if (host not in ['*', 'localhost', '127.0.0.1', '0.0.0.0']
                        and not host.startswith(('192.168.', '10.0.', '172.'))
                        and '.' in host):

                        protocol = 'https' if not host.startswith(('localhost', '127.0.0.1')) else 'http'
                        base_url = f"{protocol}://{host}"
                        return base_url

        except Exception as e:
            return

    except Exception as e:
        # 发生任何严重错误时返回默认值
        return


# 生成订单号
def generate_order_id()->str:
    """
    生成新格式的订单号：Web-YYYYMMDD{13位时间戳}{4位序号}
    例如：Web-20250621{13位时间戳}0001
    """
    import time
    from datetime import datetime

    # 获取当前时间
    now = datetime.now()
    today_str = now.strftime('%Y%m%d')  # 8位年月日
    timestamp_13 = str(int(time.time() * 1000))  # 13位时间戳

    # 使用数据库事务确保序号的唯一性
    with transaction.atomic():
        # 查询今日已生成的订单数量（从订单表中查询）
        from api.models import Order
        today_prefix = f"Web-{today_str}"
        today_orders = Order.objects.filter(
            id__startswith=today_prefix,
            created_at__date=now.date()
        ).count()

        # 生成4位序号（从0001开始）
        sequence = str(today_orders + 1).zfill(4)

        # 组合最终订单号
        order_id = f"{today_prefix}{timestamp_13}{sequence}"

        return order_id

# 验证JWT令牌
def verify_token(token, request=None):
    """
    验证JWT令牌
    返回(成功与否, 用户对象或错误信息)
    """
    try:
        # 解析Token
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])

        # 从token中获取用户ID
        user_id = payload.get('user_id')

        if not user_id:
            return False, "无效的Token"

        # 从数据库获取用户
        try:
            user = User.objects.get(user_id=user_id)
            return True, user
        except User.DoesNotExist:
            return False, "用户不存在"

    except jwt.ExpiredSignatureError:
        return False, "Token已过期"
    except jwt.InvalidTokenError:
        return False, "无效的Token"
    except Exception as e:
        return False, f"验证失败: {str(e)}"


def verify_user_token_and_key(request, user_id=None, require_user_id_match=True):
    """
    通用的JWT Token验证和用户密钥获取函数

    参数:
    - request: Django请求对象
    - user_id: 可选，需要验证的用户ID（用于API签名验证）
    - require_user_id_match: 是否要求Token中的用户ID与传入的user_id匹配

    返回:
    {
        'success': bool,           # 验证是否成功
        'user_id': str,           # 用户ID
        'user_key': str,          # 用户密钥
        'is_guest': bool,         # 是否为游客用户
        'user_obj': User,         # 用户对象（仅正式用户）
        'error_response': dict,   # 错误响应（验证失败时）
        'error_message': str      # 错误信息
    }
    """
    result = {
        'success': False,
        'user_id': None,
        'user_key': None,
        'is_guest': False,
        'user_obj': None,
        'error_response': None,
        'error_message': ''
    }

    # 从请求头获取Token
    token = request.headers.get('Token')

    # 如果请求头中没有Token，尝试从Cookie中获取
    if not token and 'token' in request.COOKIES:
        token = request.COOKIES.get('token')

    if not token:
        result['error_message'] = '缺少Token'
        result['error_response'] = JsonResponse({
            'code': 401,
            'msg': '未授权访问，缺少Token'
        })
        return result

    try:
        # 解析Token
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        token_user_id = payload.get('user_id')

        if not token_user_id:
            result['error_message'] = 'Token中缺少用户ID'
            result['error_response'] = JsonResponse({
                'code': 401,
                'msg': 'Token中缺少用户ID'
            })
            return result

        # 如果需要验证用户ID匹配且提供了user_id参数
        if require_user_id_match and user_id and token_user_id != user_id:
            result['error_message'] = 'Token中的用户ID与请求不匹配'
            result['error_response'] = JsonResponse({
                'code': 401,
                'msg': 'Token验证失败'
            })
            return result

        # 检查是否为游客用户
        if payload.get('guest', False):
            # 游客用户，直接从Token中获取user_key
            user_key = payload.get('user_key')
            if not user_key:
                result['error_message'] = '游客Token中缺少用户密钥'
                result['error_response'] = JsonResponse({
                    'code': 401,
                    'msg': '无效的游客Token'
                })
                return result

            result.update({
                'success': True,
                'user_id': token_user_id,
                'user_key': user_key,
                'is_guest': True
            })
            return result
        else:
            # 正式用户，从数据库获取user_key
            try:
                user = User.objects.get(user_id=token_user_id)
                result.update({
                    'success': True,
                    'user_id': token_user_id,
                    'user_key': str(user.user_key),
                    'is_guest': False,
                    'user_obj': user
                })
                return result
            except User.DoesNotExist:
                result['error_message'] = '用户不存在'
                result['error_response'] = JsonResponse({
                    'code': 401,
                    'msg': '用户不存在'
                })
                return result

    except jwt.ExpiredSignatureError:
        result['error_message'] = 'Token已过期'
        result['error_response'] = JsonResponse({
            'code': 401,
            'msg': 'Token已过期'
        })
        return result
    except jwt.InvalidTokenError:
        result['error_message'] = '无效的Token'
        result['error_response'] = JsonResponse({
            'code': 401,
            'msg': '无效的Token'
        })
        return result
    except Exception as e:
        result['error_message'] = f'Token验证异常: {str(e)}'
        result['error_response'] = JsonResponse({
            'code': 500,
            'msg': '服务器内部错误'
        })
        return result


# 生成JWT令牌
def generate_tokens(user):
    """
    为用户生成访问令牌和刷新令牌
    """
    # 获取当前时间戳
    timestamp = int(time.time())
    
    # 计算过期时间
    access_expires = timestamp + int(settings.JWT_ACCESS_TOKEN_LIFETIME.total_seconds())
    refresh_expires = timestamp + int(settings.JWT_REFRESH_TOKEN_LIFETIME.total_seconds())
    
    # 创建访问令牌载荷
    access_payload = {
        'user_id': str(user.user_id),
        'email': user.email,
        'user_key': str(user.user_key),
        'exp': access_expires,  # 使用配置中的访问令牌有效期
        'iat': timestamp
    }
    
    # 创建刷新令牌载荷
    refresh_payload = {
        'user_id': str(user.user_id),
        'type': 'refresh',
        'exp': refresh_expires,  # 使用配置中的刷新令牌有效期
        'iat': timestamp
    }
    
    # 生成令牌
    access_token = jwt.encode(access_payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    refresh_token = jwt.encode(refresh_payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

    # 确保令牌为字符串类型（兼容不同版本的PyJWT）
    if isinstance(access_token, bytes):
        access_token = access_token.decode('utf-8')
    if isinstance(refresh_token, bytes):
        refresh_token = refresh_token.decode('utf-8')

    return access_token, refresh_token


# 处理用户登录API
@csrf_exempt
@require_POST
@api_view(['POST'])
@permission_classes([AllowAny])
def user_login(request):
    try:
        data = json.loads(request.body)
        email = data.get('email')
        password = data.get('password')
        
        # 验证用户
        user = authenticate(request, email=email, password=password)
        
        if user is not None:
            try:
                # 使用UserService获取用户信息
                from utils.shared_services.common.auth_service import AuthService
                from utils.shared_services.user.user_service import UserService
                
                # 创建服务上下文
                context = AuthService.create_context_from_user_request(request)
                context['user'] = user  # 添加用户对象到上下文
                
                # 使用用户服务
                user_service = UserService(context)
                
                # 获取用户信息
                user_data = user_service.get_user_info(format_type='user')
                
                # 生成JWT令牌
                access_token, refresh_token = generate_tokens(user)
                
                # 创建响应对象
                response = JsonResponse({
                    'success': True, 
                    'code': 200,
                    'message': '登录成功',
                    'user': {
                        'id': user_data.get('user_id'),
                        'username': user_data.get('username'),
                        'email': user_data.get('email'),
                        'user_key': user_data.get('user_key'),
                        'membership_level': user_data.get('membership_level', 'NormalUser'),
                        'balance': user_data.get('balance', 0),
                        'refresh_token': refresh_token
                    }
                })
                
            except Exception as e:
                # 如果服务层出错，回退到原有逻辑
                access_token, refresh_token = generate_tokens(user)
                
                response = JsonResponse({
                    'success': True, 
                    'code': 200,
                    'message': '登录成功',
                    'user': {
                        'id': str(user.user_id),
                        'username': user.username,
                        'email': user.email,
                        'user_key': str(user.user_key),
                        'refresh_token': refresh_token
                    }
                })
            
            # 将token放在响应头部
            response['Token'] = access_token
            
            # 将token同时设置到Cookie中，确保所有页面都能访问
            response.set_cookie('token', access_token, path='/')
            
            return response
        else:
            return JsonResponse({'success': False, 'message': '邮箱或密册错误'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'登录失败: {str(e)}'})


# 处理发送邮箱验证码API
@csrf_exempt
@require_POST
@api_view(['POST'])
@permission_classes([AllowAny])
def send_email_code(request):
    try:
        data = json.loads(request.body)
        email = data.get('email')
        
        if not email:
            return JsonResponse({'success': False, 'message': '请提供有效的邮箱地址'})
        
        # 生成6位随机验证码
        code = ''.join(random.choices(string.digits, k=6))
        
        # 设置过期时间为10分钟后
        expires_at = timezone.now() + timedelta(minutes=10)
        # 保存验证码到数据库
        EmailVerificationCode.objects.create(
            email=email,
            code=code,
            expires_at=expires_at
        )
        
        # 发送邮件
        subject = '商城注册验证码'
        message = f'您的注册验证码为: {code}，有效期10分钟。'
        from_email = settings.DEFAULT_FROM_EMAIL
        recipient_list = [email]
        s = send_mail(
            subject=subject,
            message=message,
            from_email=from_email,
            recipient_list=recipient_list,
            fail_silently=False
        )
        return JsonResponse({'success': True, 'message': '验证码已发送，请查收邮件'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'发送验证码失败: {str(e)}'})


# 处理用户注册API
@csrf_exempt
@require_POST
@api_view(['POST'])
@permission_classes([AllowAny])
def user_register(request):
    try:
        data = json.loads(request.body)
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        email_code = data.get('email_code')
        
        # 验证用户名是否已存在
        if User.objects.filter(username=username).exists():
            return JsonResponse({'success': False, 'message': '用户名已存在'})
        
        # 验证邮箱是否已存在
        if User.objects.filter(email=email).exists():
            return JsonResponse({'success': False, 'message': '邮箱已被注册'})
        
        # 验证密码长度
        if len(password) < 8:
            return JsonResponse({'success': False, 'message': '密码长度不能少于8位'})
        
        # 验证邮箱验证码
        try:
            email_verification = EmailVerificationCode.objects.filter(
                email=email,
                code=email_code,
                is_used=False
            ).latest('created_at')
            
            # 检查验证码是否过期
            if email_verification.is_expired:
                return JsonResponse({'success': False, 'message': '验证码已过期，请重新获取'})
            
            # 标记验证码为已使用
            email_verification.is_used = True
            email_verification.save()
            
        except EmailVerificationCode.DoesNotExist:
            return JsonResponse({'success': False, 'message': '验证码错误或已使用'})
        
        # 计算邮箱的SHA256哈希值
        # 确保邮箱是字符串类型
        email_str = str(email) if not isinstance(email, str) else email
        full_hash = hashlib.sha256(email_str.encode()).hexdigest()
        
        # 取前32位哈希值并创建一个UUID对象
        # 使用uuid5，这样可以从哈希字符串生成一个确定性的UUID
        # 使用 uuid.NAMESPACE_DNS 作为命名空间（可以使用任何标准命名空间）
        user_id_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, full_hash[:32])
        
        # 创建新用户
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            user_id=user_id_uuid  # 传入UUID对象
        )
        
        return JsonResponse({'success': True, 'message': '注册成功'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'注册失败: {str(e)}'})


# 验证Token有效性API（需要认证）
@csrf_exempt
@api_view(['GET'])
def verify_token_api(request):
    # 从请求头获取令牌
    token = request.headers.get('Token')
    if not token:
        return JsonResponse({'success': False, 'message': '未授权访问'})
    
    # 验证令牌
    is_valid, result = verify_token(token, request)
    
    if not is_valid:
        return JsonResponse({'success': False, 'message': result})
    
    # 获取用户对象
    user = result   # 此时result是User对象
    """
    # 自定义用户模型
    class User(AbstractBaseUser, PermissionsMixin):
    username = models.CharField(max_length=50, unique=True, verbose_name='用户名')
    email = models.EmailField(max_length=100, unique=True, verbose_name='邮箱')
    user_key = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='用户密钥')
    is_active = models.BooleanField(default=True, verbose_name='激活状态')
    is_staff = models.BooleanField(default=False, verbose_name='管理员状态')
    date_joined = models.DateTimeField(default=timezone.now, verbose_name='注册时间')
    # 添加用户会员等级 用户余额 用户累计消费 用户唯一ID
    membership_level = models.CharField(max_length=20, default='普通用户', verbose_name='会员等级')
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, verbose_name='用户余额')
    total_spent = models.DecimalField(max_digits=12, decimal_places=2, default=0.00, verbose_name='累计消费')
    user_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='用户唯一ID')
    """

    return JsonResponse({
        'success': True, 
        'user': {
            'id': str(user.user_id),
            'username': user.username,
            'email': user.email,
        }
    })

# 获取用户信息API（需要验证Token）
@csrf_exempt
@api_view(['GET'])
def get_user_info(request):
    # 从请求头获取令牌
    token = request.headers.get('Token')
    if not token:
        return JsonResponse({'success': False, 'message': '未授权访问'})
    
    # 验证令牌
    is_valid, result = verify_token(token, request)
    
    if not is_valid:
        return JsonResponse({'success': False, 'message': result})
    
    # 获取用户对象（此时result是User对象）
    user = result
    
    try:
        # 使用UserService获取用户信息
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.user.user_service import UserService
        
        # 创建服务上下文
        context = AuthService.create_context_from_user_request(request)
        context['user'] = user  # 添加用户对象到上下文
        
        # 使用用户服务
        user_service = UserService(context)
        
        # 获取用户信息
        user_data = user_service.get_user_info(format_type='user')
        
        return JsonResponse({
            'success': True,
            'user': {
                'id': user_data.get('user_id'),
                'username': user_data.get('username'),
                'email': user_data.get('email'),
                'user_key': user_data.get('user_key'),
                'date_joined': user_data.get('date_joined'),
                'membership_level': user_data.get('membership_level', 'NormalUser'),
                'balance': user_data.get('balance', 0)
            }
        })
    
    except Exception as e:
        return JsonResponse({'success':False})
        

# 刷新令牌API
@csrf_exempt
@require_POST
@api_view(['POST'])
@permission_classes([AllowAny])
def refresh_token_api(request):
    try:
        data = json.loads(request.body)
        refresh_token = data.get('refresh_token')
        
        if not refresh_token:
            return JsonResponse({'success': False, 'message': '参数不完整'})
        
        # 验证刷新令牌
        try:
            # 解析令牌获取用户ID
            payload = jwt.decode(refresh_token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            user_id = payload.get('user_id')
            token_type = payload.get('type')
            
            if not user_id or token_type != 'refresh':
                return JsonResponse({'success': False, 'message': '无效的刷新令牌'})
            
            # 获取用户
            user = User.objects.get(user_id=user_id)
            
            # 生成新的令牌
            access_token, new_refresh_token = generate_tokens(user)
            
            # 构建响应
            response = JsonResponse({
                'success': True,
                'message': '令牌刷新成功',
                'data': {
                    'refresh_token': new_refresh_token
                }
            })
            
            response['Token'] = access_token
            
            # 将新的token同时设置到Cookie中
            response.set_cookie('token', access_token, path='/')
            
            return response
            
        except jwt.ExpiredSignatureError:
            return JsonResponse({'success': False, 'message': '刷新令牌已过期'})
        except jwt.InvalidTokenError:
            return JsonResponse({'success': False, 'message': '无效的刷新令牌'})
        except User.DoesNotExist:
            return JsonResponse({'success': False, 'message': '用户不存在'})
            
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'刷新令牌失败: {str(e)}'})


# 获取用户API - 支持正式用户和游客
@csrf_exempt
@require_http_methods(["GET", "POST"])
def get_user(request):
    """
    获取用户或创建游客用户
    GET 或 POST /user/GetUser
    """
    # 从请求头获取令牌
    token = request.headers.get('Token')
    
    # 如果请求头中没有Token，尝试从Cookie中获取
    if not token and 'token' in request.COOKIES:
        token = request.COOKIES.get('token')
    
    # 有Token的情况
    if token:
        try:
            # 验证令牌
            is_valid, result = verify_token(token, request)
            
            # Token有效，正式用户访问
            if is_valid:
                user = result
                
                try:
                    # 使用UserService获取用户信息
                    from utils.shared_services.common.auth_service import AuthService
                    from utils.shared_services.user.user_service import UserService
                    
                    # 创建服务上下文
                    context = AuthService.create_context_from_user_request(request)
                    context['user'] = user  # 添加用户对象到上下文
                    
                    # 使用用户服务
                    user_service = UserService(context)
                    
                    # 获取用户信息
                    user_data = user_service.get_user_info(format_type='user')
                    
                    # 刷新令牌
                    access_token, refresh_token = generate_tokens(user)
                    
                    # 构建响应
                    response = JsonResponse({
                        'code': 200,
                        'user': {
                            'type': '正式用户',
                            'id': user_data.get('user_id'),
                            'username': user_data.get('username'),
                            'email': user_data.get('email'),
                            'user_key': user_data.get('user_key'),
                            'membership_level': user_data.get('membership_level', 'NormalUser'),
                            'balance': user_data.get('balance', 0),
                            'date_joined': user_data.get('date_joined'),
                            'refresh_token': refresh_token
                        }
                    })
                    
                except Exception as e:
                    return JsonResponse({'success':False})
                
                # 设置新Token
                response['Token'] = access_token
                
                # 更新Cookie中的Token
                response.set_cookie('token', access_token, path='/')
                
                return response
            
            # Token无效或过期，视为游客
            else:
                # 创建游客用户
                return create_guest_user(request)
                
        except Exception as e:
            # 出现异常，视为游客
            return create_guest_user(request)
    
    # 无Token的情况，视为游客
    else:
        return create_guest_user(request)


# 创建游客用户
def create_guest_user(request):
    """
    为游客用户创建临时令牌，不再存储游客数据到数据库
    """
    # 生成随机的游客ID和密钥
    guest_id = str(uuid.uuid4())
    guest_key = str(uuid.uuid4())
    
    # 获取当前时间戳
    timestamp = int(time.time())
    
    # 计算过期时间
    access_expires = timestamp + int(settings.JWT_ACCESS_TOKEN_LIFETIME.total_seconds())
    
    # 创建访问令牌载荷
    payload = {
        'user_id': guest_id,
        'user_key': guest_key,
        'guest': True,  # 标记为游客
        'exp': access_expires,  # 使用配置中的访问令牌有效期
        'iat': timestamp
    }
    
    # 生成令牌
    token = jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
    
    # 构建响应
    response = JsonResponse({
        'success': True,
        'code': 200,
        'user': {
            'type': '游客用户',
            'id': guest_id,
            'user_key': guest_key,
            'guest': True
        }
    })
    
    # 设置Token
    response['Token'] = token
    
    # 同时将Token设置到Cookie中
    response.set_cookie('token', token, path='/')
    
    return response


# 重置密码API
@csrf_exempt
@require_POST
@api_view(['POST'])
@permission_classes([AllowAny])
def reset_password(request):
    try:
        data = json.loads(request.body)
        email = data.get('email')
        email_code = data.get('email_code')
        new_password = data.get('new_password')
        
        if not email or not email_code or not new_password:
            return JsonResponse({'success': False, 'message': '请提供所有必填字段'})
        
        # 验证密码长度
        if len(new_password) < 8:
            return JsonResponse({'success': False, 'message': '密码长度不能少于8位'})
        
        # 验证用户是否存在
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return JsonResponse({'success': False, 'message': '该邮箱未注册'})
        
        # 验证邮箱验证码
        try:
            email_verification = EmailVerificationCode.objects.filter(
                email=email,
                code=email_code,
                is_used=False
            ).latest('created_at')
            
            # 检查验证码是否过期
            if email_verification.is_expired:
                return JsonResponse({'success': False, 'message': '验证码已过期，请重新获取'})
            
            # 标记验证码为已使用
            email_verification.is_used = True
            email_verification.save()
            
        except EmailVerificationCode.DoesNotExist:
            return JsonResponse({'success': False, 'message': '验证码错误或已使用'})
        
        # 更新用户密码（使用加盐处理）
        # 生成新的盐值
        new_salt = User.objects.generate_salt()
        user.password_salt = new_salt

        # 使用自定义盐值处理密码
        salted_password = User.objects.hash_password_with_salt(new_password, new_salt)
        # 再使用Django的set_password进行标准哈希处理
        user.set_password(salted_password)
        user.save()
        
        return JsonResponse({'success': True, 'message': '密码重置成功'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'密码重置失败: {str(e)}'})


# 获取商品分类API
@csrf_exempt
@require_POST
@api_view(['POST'])
@permission_classes([AllowAny])
def get_categories(request):

    try:
        # 获取请求数据
        data = request.POST
        user_id = data.get('userId')
        sign = data.get('sign')
        
        # 验证参数是否完整
        if not user_id or not sign:
            return JsonResponse({
                'code': 400,
                'message': '参数不完整'
            })
        
        # 使用通用验证函数
        auth_result = verify_user_token_and_key(request, user_id, require_user_id_match=True)
        if not auth_result['success']:
            return auth_result['error_response']

        user_key = auth_result['user_key']
        is_guest = auth_result['is_guest']
            
        expected_sign = hashlib.md5((user_id + user_key).encode()).hexdigest()
        
        if sign != expected_sign:
            return JsonResponse({
                'code': 403,
                'message': '签名验证失败'
            })
        
        # 使用CategoryService获取分类数据
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.category.category_service import CategoryService
        
        # 创建服务上下文
        context = AuthService.create_context_from_user_request(request)
        
        # 使用分类服务
        category_service = CategoryService(context)
        categories = category_service.get_category_list(include_children=True, format_type='user')
        
        # 转换为用户端期望的格式
        result = []
        for category in categories:
            category_data = {
                'name': category['name'],
                'id': category['id'],
                'image': category['image'],
                'children': []
            }
            
            # 处理子分类
            for child in category.get('children', []):
                category_data['children'].append({
                    'name': child['name'],
                    'id': child['id'],
                    'parentId': child['parent_id'],
                    'image': child['image']
                })
            
            result.append(category_data)
        
        # 返回成功响应
        return JsonResponse({
            'code': 200,
            'data': result
        })
        
    except Exception as e:
        # 记录异常并返回错误响应
        return JsonResponse({
            'code': 500,
            'message': f'服务器错误: {str(e)}'
        })


def calculate_actual_price(base_price, price_template, user_membership_key):
    """
    根据基础价格、价格模板和用户会员等级计算实际价格

    参数:
    - base_price: 商品基础价格 (Decimal类型)
    - price_template: PriceTemplate模型实例
    - user_membership_key: 用户会员等级标识 ("NormalUser" 或 MemberLevel的id)

    返回:
    - 计算后的实际价格 (字符串格式)
    """
    try:
        # 如果没有价格模板，返回原价（条件性处理超过两位小数的情况）
        if not price_template:
            base_price = Decimal(str(base_price))
            price_times_100 = base_price * Decimal('100')
            if price_times_100 % 1 != 0:
                # 存在两位小数以上的精度，保留两位小数并加0.01防止亏本
                base_price = base_price.quantize(Decimal('0.01'), rounding='ROUND_UP') + Decimal('0.01')
            else:
                # 价格本身就是两位小数或更少，直接使用
                pass
            return str(base_price)

        # 解析价格模板的JSON数据
        try:
            pricing_data = json.loads(price_template.data_json)
        except json.JSONDecodeError:
            base_price = Decimal(str(base_price))
            price_times_100 = base_price * Decimal('100')
            if price_times_100 % 1 != 0:
                # 存在两位小数以上的精度，保留两位小数并加0.01防止亏本
                base_price = base_price.quantize(Decimal('0.01'), rounding='ROUND_UP') + Decimal('0.01')
            else:
                # 价格本身就是两位小数或更少，直接使用
                pass
            return str(base_price)

        # 获取用户等级对应的加价值
        markup_value = pricing_data.get(user_membership_key)

        # 如果找不到用户等级对应的加价值，尝试使用普通用户的加价值
        if markup_value is None:
            markup_value = pricing_data.get("NormalUser", 0)

        # 转换为数值类型
        markup_value = Decimal(str(markup_value))
        base_price = Decimal(str(base_price))

        # 根据模板类型计算实际价格
        if price_template.type == "1":
            # 固定金额加价
            actual_price = base_price + markup_value
        elif price_template.type == "2":
            # 百分比加价
            actual_price = base_price * (Decimal('1') + markup_value / Decimal('100'))
        else:
            # 未知的模板类型，使用原价
            actual_price = base_price

        # 确保价格不为负数
        if actual_price < 0:
            actual_price = Decimal('0')

        # 检查是否需要处理超过两位小数的情况
        # 将价格乘以100，如果不是整数则说明有超过两位小数的部分
        price_times_100 = actual_price * Decimal('100')
        if price_times_100 % 1 != 0:
            # 存在两位小数以上的精度，保留两位小数并加0.01防止亏本
            actual_price = actual_price.quantize(Decimal('0.01'), rounding='ROUND_UP') + Decimal('0.01')
        else:
            # 价格本身就是两位小数或更少，直接使用
            pass

        return str(actual_price)

    except Exception as e:
        # 即使是异常情况下的原价，也要检查是否需要处理超过两位小数的情况
        base_price = Decimal(str(base_price))
        price_times_100 = base_price * Decimal('100')
        if price_times_100 % 1 != 0:
            # 存在两位小数以上的精度，保留两位小数并加0.01防止亏本
            base_price = base_price.quantize(Decimal('0.01'), rounding='ROUND_UP') + Decimal('0.01')
        else:
            # 价格本身就是两位小数或更少，直接使用
            pass
        return str(base_price)


# 获取商品详情API
@csrf_exempt
@require_POST
@api_view(['POST'])
@permission_classes([AllowAny])
def get_product_info(request):
    """
    获取单个商品详情数据
    根据商品ID返回该商品的详细信息
    需要验证用户签名
    """
    try:
        # 获取请求数据
        data = request.POST
        product_id = data.get('id')
        user_id = data.get('userId')
        sign = data.get('sign')

        # 验证参数是否完整
        if not product_id or not user_id or not sign:
            return JsonResponse({
                'code': 400,
                'msg': '参数不完整'
            })

        auth_result = verify_user_token_and_key(request, user_id, require_user_id_match=True)
        if not auth_result['success']:
            return auth_result['error_response']

        user_key = auth_result['user_key']
        is_guest = auth_result['is_guest']

        expected_sign = hashlib.md5((product_id + user_id + user_key).encode()).hexdigest()

        if sign != expected_sign:
            return JsonResponse({
                'code': 403,
                'msg': '签名验证失败'
            })

        # 使用ProductService获取商品详情
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.product.product_service import ProductService
        
        # 创建服务上下文
        context = AuthService.create_context_from_user_request(request)
        
        # 使用商品服务
        product_service = ProductService(context)
        
        # 获取商品详情
        try:
            product_data = product_service.get_product_detail(product_id, format_type='user')
        except Exception as e:
            if "不存在" in str(e):
                return JsonResponse({
                    'code': 404,
                    'msg': '商品不存在'
                })
            else:
                raise e
        
        # 如果是对接商品，尝试更新信息
        if product_data.get('type') == '3':
            try:
                product_service.update_docking_product(product_id)
                # 重新获取更新后的商品信息
                product_data = product_service.get_product_detail(product_id, format_type='user')
                print(f"前台商品详情：异步更新对接商品 {product_id} 成功")
            except Exception as e:

                print(f"前台商品详情：更新对接商品 {product_id} 信息失败: {str(e)}")
        
        from api.models import Goods
        try:
            goods = Goods.objects.get(id=product_id)
            
            # 处理图片URL
            image_url = product_data.get('image', '')
            if not image_url or not image_url.strip():
                image_url = f'/api/get_local_image_product?id={goods.id}&placeholder=true'

            goods_data = {
                'id': product_data.get('id'),
                'name': product_data.get('name'),
                'price': product_data.get('actual_price', product_data.get('price', '')),
                'image': image_url,
                'image_type': goods.image_type or '2',
                'PriceTemplate': goods.price_template.id if goods.price_template else '',
                'type': product_data.get('type'),
                'sales_count': goods.sales_count,
                'category': product_data.get('category_id', ''),
                'info': product_data.get('info', ''),
                'attach': product_data.get('attach', []),
                'stock': product_data.get('stock', 0),
                'created_at': product_data.get('created_at', ''),
                'updated_at': product_data.get('updated_at', ''),
                'status': product_data.get('status')
            }
            
        except Goods.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'msg': '商品不存在'
            })

        # 成功响应
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': [goods_data]  # 按照要求返回数组格式
        })

    except Exception as e:
        # 记录异常并返回错误响应
        return JsonResponse({
            'code': 500,
            'msg': f'服务器错误: {str(e)}'
        })

# 获取支付方式列表API
@csrf_exempt
@require_POST
@api_view(['POST'])
@permission_classes([AllowAny])
def get_payment_methods(request):
    """
    获取支付方式列表数据
    返回所有启用状态的支付方式
    需要验证用户签名
    """
    try:
        # 获取请求数据
        data = request.POST
        user_id = data.get('userId')
        sign = data.get('sign')

        # 验证参数是否完整
        if not user_id or not sign:
            return JsonResponse({
                'code': 400,
                'msg': '参数不完整'
            })

        # 使用通用验证函数
        auth_result = verify_user_token_and_key(request, user_id, require_user_id_match=True)
        if not auth_result['success']:
            return auth_result['error_response']

        user_key = auth_result['user_key']

        expected_sign = hashlib.md5((user_id + user_key).encode()).hexdigest()

        if sign != expected_sign:
            return JsonResponse({
                'code': 403,
                'msg': '签名验证失败'
            })

        # 使用PaymentService获取支付方式列表
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.payment.payment_service import PaymentService
        
        # 创建服务上下文
        context = AuthService.create_context_from_user_request(request)
        
        # 使用支付服务
        payment_service = PaymentService(context)
        
        # 获取支付方式列表（用户端只显示激活的）
        payment_methods = payment_service.get_payment_methods(format_type='user')
        
        # 转换为原有的响应格式
        result = []
        for method in payment_methods:
            payment_data = {
                'id': method.get('id'),
                'name': method.get('name'),
                'fee': method.get('fee'),
                'data': method.get('data', []),
                'interface_type': method.get('interface_type')
            }
            result.append(payment_data)
        # 返回成功响应
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': result
        })

    except Exception as e:
        # 记录异常并返回错误响应
        return JsonResponse({
            'code': 500,
            'msg': f'服务器错误: {str(e)}'
        })


# 获取商品列表API
@csrf_exempt
@require_POST
@api_view(['POST'])
@permission_classes([AllowAny])
def get_product_list(request):
    """
    获取商品列表数据 - 支持分页和搜索功能
    根据分类ID或商品名称返回商品列表
    需要验证用户签名
    """
    try:
        # 获取请求数据
        data = request.POST
        category_id = data.get('categoryId')
        name = data.get('name')
        user_id = data.get('userId')
        sign = data.get('sign')

        # 获取分页参数
        page = int(data.get('page', 1))
        page_size = min(int(data.get('page_size', 20)), 100)  # 最大100条

        # 验证参数是否完整
        if not (category_id or name) or not user_id or not sign:
            return JsonResponse({
                'code': 400,
                'msg': '参数不完整'
            })

        # 参数验证
        if page < 1:
            page = 1
        if page_size < 1:
            page_size = 20

        # 使用通用验证函数
        auth_result = verify_user_token_and_key(request, user_id, require_user_id_match=True)
        if not auth_result['success']:
            return auth_result['error_response']

        user_key = auth_result['user_key']
        is_guest = auth_result['is_guest']

        # 根据是搜索还是分类浏览来验证签名
        if name:
            # 按名称搜索
            expected_sign = hashlib.md5((name + user_id + user_key).encode()).hexdigest()
        else:
            # 按分类浏览
            expected_sign = hashlib.md5((category_id + user_id + user_key).encode()).hexdigest()

        if sign != expected_sign:
            return JsonResponse({
                'code': 403,
                'msg': '签名验证失败'
            })

        # 使用ProductService获取商品列表
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.product.product_service import ProductService
        
        # 创建服务上下文
        context = AuthService.create_context_from_user_request(request)
        
        # 使用商品服务
        product_service = ProductService(context)
        
        # 构建过滤条件和分页参数
        filters = {
            'status': '1'  # 只显示激活的商品
        }
        if name:
            filters['name'] = name
        else:
            filters['category_id'] = category_id
        
        pagination = {
            'page': page,
            'page_size': page_size
        }
        
        # 获取商品列表
        result = product_service.get_product_list(
            filters=filters,
            pagination=pagination,
            format_type='user'
        )
        
        # 处理对接商品的后台更新（保持原有逻辑）
        docking_products = [p for p in result['list'] if p.get('type') == '3']
        if docking_products:
            try:
                # 后台异步更新对接商品信息 - 不阻塞响应
                def background_update():
                    try:
                        for product in docking_products:
                            try:
                                product_service.update_docking_product(product['id'])
                            except Exception as e:
                                print(f"后台更新对接商品 {product['id']} 失败: {str(e)}")
                        print(f"后台更新：前台商品列表更新 {len(docking_products)} 个对接商品完成")
                    except Exception as e:
                        print(f"后台更新：前台商品列表批量更新失败: {str(e)}")

                # 提交到线程池执行，不等待结果
                import threading
                update_thread = threading.Thread(target=background_update, daemon=True)
                update_thread.start()

                print(f"前台已启动后台线程更新 {len(docking_products)} 个对接商品")

            except Exception as e:
                print(f"前台启动后台更新线程失败: {str(e)}")
        
        # 转换为原有的响应格式
        items = []
        for product in result['list']:
            # 获取原始商品数据以保持兼容性
            try:
                from api.models import Goods
                goods = Goods.objects.get(id=product['id'])
                
                # 处理图片URL
                image_url = product.get('image', '')
                if not image_url or not image_url.strip():
                    image_url = f'/api/get_local_image_product?id={goods.id}&placeholder=true'
                
                goods_data = {
                    'id': product.get('id'),
                    'name': product.get('name'),
                    'price': product.get('actual_price', product.get('price', '')),
                    'image': image_url,
                    'image_type': goods.image_type or '2',
                    'PriceTemplate': goods.price_template.id if goods.price_template else '',
                    'type': product.get('type'),
                    'sales_count': goods.sales_count,
                    'category': product.get('category_id', ''),
                    'info': product.get('info', ''),
                    'attach': product.get('attach_data', []),
                    'created_at': product.get('created_at', ''),
                    'updated_at': product.get('updated_at', ''),
                    'status': product.get('status')
                }
                
                items.append(goods_data)
                
            except Exception as e:
                # 如果获取原始数据失败，使用服务层数据
                items.append({
                    'id': product.get('id'),
                    'name': product.get('name'),
                    'price': product.get('actual_price', product.get('price', '')),
                    'image': product.get('image', ''),
                    'image_type': '2',
                    'PriceTemplate': '',
                    'type': product.get('type'),
                    'sales_count': 0,
                    'category': product.get('category_id', ''),
                    'info': product.get('info', ''),
                    'attach': product.get('attach_data', []),
                    'created_at': product.get('created_at', ''),
                    'updated_at': product.get('updated_at', ''),
                    'status': product.get('status')
                })

        # 构建分页响应
        pagination_data = {
            'current_page': result.get('page', 1),
            'page_size': result.get('page_size', 20),
            'total': result.get('total', 0),
            'total_pages': result.get('total_pages', 1),
            'has_next': result.get('has_next', False),
            'has_prev': result.get('has_prev', False)
        }

        # 成功响应
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': {
                'items': items,
                'pagination': pagination_data
            }
        })

    except ValueError as e:
        return JsonResponse({
            'code': 400,
            'msg': f'参数错误: {str(e)}'
        })
    except Exception as e:
        # 记录异常并返回错误响应
        return JsonResponse({
            'code': 500,
            'msg': f'服务器错误: {str(e)}'
        })


# 订单结算页面
def order_page(request):

    try:
        # 获取商品ID参数
        product_id = request.GET.get('id')

        if not product_id:
            # 商品ID为空，返回自定义错误页面
            context = {
                'error_title': '参数错误',
                'error_message': '商品ID不能为空，请检查访问链接是否正确。',
                'error_icon': 'fa-exclamation-circle'
            }
            return smart_render(request, get_template_path('product-error.html'), context)

        # 获取商品信息
        try:
            goods = Goods.objects.get(id=product_id)
        except Goods.DoesNotExist:
            # 商品不存在，返回自定义错误页面
            context = {
                'error_title': '商品不存在',
                'error_message': f'商品ID "{product_id}" 不存在或已被删除。请检查商品链接是否正确，或浏览其他商品。',
                'error_icon': 'fa-box-open'
            }
            return smart_render(request, get_template_path('product-error.html'), context)

        # 判断是否为对接商品，如果是则更新商品信息
        if goods.type == '3':
            try:
                # 导入异步更新函数
                from api.views import update_docking_good_info_async

                # 使用异步更新单个商品
                async def update_single_good():
                    connector = aiohttp.TCPConnector(limit=10)
                    timeout = aiohttp.ClientTimeout(total=3)

                    async with aiohttp.ClientSession(
                        connector=connector,
                        timeout=timeout
                    ) as session:
                        return await update_docking_good_info_async(session, goods.id)

                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    update_result = loop.run_until_complete(update_single_good())

                    if update_result.get('code') == 200:
                        # 更新成功，重新获取商品信息以确保数据最新
                        goods.refresh_from_db()
                        print(f"订单页面：异步更新对接商品 {goods.id} 成功")
                    else:
                        print(f"订单页面：异步更新对接商品 {goods.id} 失败: {update_result.get('msg')}")

                finally:
                    loop.close()

            except Exception as e:
                # 更新失败不影响页面显示，只记录错误
                print(f"订单页面：更新对接商品 {goods.id} 信息失败: {str(e)}")

        # 检查商品状态
        if goods.status != '1':  # 商品未上架
            # 商品已下架，返回自定义错误页面
            status_messages = {
                '2': '商品已下架，暂时无法购买。'
            }
            context = {
                'error_title': '商品不可购买',
                'error_message': status_messages.get(goods.status, '商品状态异常，暂时无法购买。'),
                'error_icon': 'fa-ban'
            }
            return smart_render(request, get_template_path('product-error.html'), context)

        # 获取用户信息和会员等级
        user_membership_key = "NormalUser"  # 默认为普通用户
        user_info = None

        # 使用通用验证函数（不要求用户ID匹配，因为这是页面访问）
        auth_result = verify_user_token_and_key(request, require_user_id_match=False)

        if auth_result['success']:
            # Token验证成功
            if auth_result['is_guest']:
                # 游客用户
                user_membership_key = "NormalUser"
                user_info = {
                    'type': '游客用户',
                    'id': auth_result['user_id'],
                    'user_key': auth_result['user_key'],
                    'guest': True
                }
            else:
                # 正式用户
                user = auth_result['user_obj']
                membership_level_value = user.membership_level

                # 设置会员等级标识
                if membership_level_value == "普通用户":
                    user_membership_key = "NormalUser"
                else:
                    user_membership_key = membership_level_value

                user_info = {
                    'type': '正式用户',
                    'id': str(user.user_id),
                    'username': user.username,
                    'email': user.email,
                    'user_key': str(user.user_key),
                    'membership_level': membership_level_value
                }
        else:
            # Token验证失败或无Token，使用默认设置
            user_membership_key = "NormalUser"
            user_info = None

        # 计算实际价格
        try:
            actual_price = calculate_actual_price(goods.price, goods.price_template, user_membership_key)
        except Exception as e:
            # 价格计算异常，使用原价
            actual_price = str(goods.price)

        # 处理商品参数
        attach_data = []
        if goods.attach:
            try:
                attach_json = json.loads(goods.attach)
                if isinstance(attach_json, list):
                    attach_data = attach_json
                elif isinstance(attach_json, dict):
                    # 如果是字典格式，转换为列表格式
                    for key, value in attach_json.items():
                        attach_data.append({
                            'name': key,
                            'tip': str(value)
                        })
            except json.JSONDecodeError:
                # JSON解析失败，保持空列表
                attach_data = []

        # 处理库存状态
        stock_status = "充足"
        stock_class = ""
        if goods.status == "3":  # 售罄
            stock_status = "售罄"
            stock_class = "out"
        elif goods.stock < 10:  # 库存不足
            stock_status = f"库存不足 ({goods.stock})"
            stock_class = "low"
        else:
            stock_status = f"库存充足 ({goods.stock}+)"
            stock_class = ""

        # 获取URL参数用于预填充（支付取消返回时使用）
        prefill_data = {
            'notice_mail': request.GET.get('notice_mail', ''),
            'payment_id': request.GET.get('payment_id', ''),
            'payment_way': request.GET.get('payment_way', ''),
            'coupon': request.GET.get('coupon', ''),
            'attach': request.GET.get('attach', '')
        }

        # 将预填充数据转换为JSON字符串，供前端使用
        prefill_data_json = json.dumps(prefill_data, ensure_ascii=False)

        # 准备传递给模板的上下文数据
        context = {
            'goods': goods,
            'actual_price': actual_price,
            'original_price': str(goods.price),
            'attach_data': attach_data,
            'stock_status': stock_status,
            'stock_class': stock_class,
            'user_info': user_info,
            'page_title': f'{goods.name} - 下单确认',
            'prefill_data_json': prefill_data_json  # 添加JSON格式的预填充数据
        }

        return smart_render(request, get_template_path('order.html'), context)

    except Exception as e:
        # 处理其他异常
        context = {
            'error_title': '页面加载失败',
            'error_message': '页面加载时发生错误，请稍后重试。如果问题持续存在，请联系客服。',
            'error_icon': 'fa-exclamation-triangle'
        }
        return smart_render(request, get_template_path('product-error.html'), context)


# 获取卡券详情API
@csrf_exempt
@require_http_methods(["GET"])
@api_view(['GET'])
@permission_classes([AllowAny])
def get_coupon_detail(request):
    """
    获取卡券详情API
    GET /user/api/couponsDetail?coupon={{实际的卡券码}}&userId={{实际的userId}}&sign={{实际签名值}}

    参数说明：
    - coupon: 卡券代码
    - userId: 用户ID
    - sign: 签名值，生成规则：MD5(coupon + userId + user_key)

    返回数据：
    - 成功时返回卡券的基本信息和可用状态
    - 失败时返回错误信息
    """
    try:
        # 获取请求参数
        coupon_code = request.GET.get('coupon')
        user_id = request.GET.get('userId')
        sign = request.GET.get('sign')

        # 验证参数是否完整
        if not coupon_code or not user_id or not sign:
            return JsonResponse({
                'code': 400,
                'msg': '参数不完整，请提供coupon、userId和sign参数'
            })

        # 使用通用验证函数
        auth_result = verify_user_token_and_key(request, user_id, require_user_id_match=True)
        if not auth_result['success']:
            return auth_result['error_response']

        user_key = auth_result['user_key']

        # 生成预期签名：coupon + userId + user_key
        expected_sign = hashlib.md5((coupon_code + user_id + user_key).encode()).hexdigest()

        if sign != expected_sign:
            return JsonResponse({
                'code': 403,
                'msg': '签名验证失败'
            })

        # 查询卡券信息
        try:
            coupon = Coupon.objects.get(code=coupon_code)
        except Coupon.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'msg': '卡券不存在'
            })

        # 检查卡券状态
        current_time = timezone.now()

        # 检查卡券是否启用
        if not coupon.is_active:
            return JsonResponse({
                'code': 400,
                'msg': '卡券已被禁用'
            })

        # 检查卡券是否已使用
        if coupon.is_used:
            return JsonResponse({
                'code': 400,
                'msg': '卡券已被使用'
            })

        # 检查卡券是否过期
        if coupon.expiry_date < current_time:
            return JsonResponse({
                'code': 400,
                'msg': '卡券已过期'
            })

        # 构建返回数据
        response_data = {
            'code': coupon.code,
            'discountType': coupon.discount_type,
            'discountValue': str(coupon.discount_value),
            'minOrderAmount': str(coupon.min_order_amount),
            'description': coupon.description or '',
            'createTime': coupon.create_time.strftime('%Y-%m-%d %H:%M:%S'),
            'expiryDate': coupon.expiry_date.strftime('%Y-%m-%d %H:%M:%S'),
            'isUsed': coupon.is_used,
            'usedTime': coupon.used_time.strftime('%Y-%m-%d %H:%M:%S') if coupon.used_time else None,
            'usedBy': coupon.used_by,
            'status': 'active'  # 能到这里说明卡券可用
        }

        # 成功响应
        return JsonResponse({
            'code': 200,
            'msg': '获取成功',
            'data': response_data
        })

    except Exception as e:
        # 记录异常并返回错误响应
        return JsonResponse({
            'code': 500,
            'msg': f'服务器错误: {str(e)}'
        })


# 订单提交API
@csrf_exempt
def submit_order_api(request):
    """
    订单提交API

    请求格式：
    {
        "id": "商品ID",
        "coupon": "优惠券代码",
        "mail": "用户邮箱",
        "attach": [
            {
                "name": "参数1值",
                "name": "参数2值"
            }
        ],
        "payment": {
            "id": "支付渠道ID",
            "way": "支付方式"
        },
        "time": "十三位时间戳",
        "sign": "md5签名"
    }

    签名规则：将请求数据json的所有键按照ASCII码排序后，在文本后衔接key值 {}+key 进行md5签名
    """
    if request.method != 'POST':
        return JsonResponse({
            'code': 405,
            'msg': '请求方法不允许'
        })

    # 获取User-Agent并判断设备类型
    user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
    device_type = 'desktop'  # 默认为桌面端

    # 检查是否为移动设备
    mobile_keywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone']
    if any(keyword in user_agent for keyword in mobile_keywords):
        device_type = 'mobile'

    try:
        # 解析JSON数据
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({
                'code': 400,
                'msg': '无效的JSON数据'
            })

        # 验证必填参数
        required_fields = ['id', 'mail', 'payment', 'time', "attach"]
        for field in required_fields:
            if field not in data:
                return JsonResponse({
                    'code': 400,
                    'msg': f'缺少必填参数: {field}'
                })

        # 获取参数
        product_id = data.get('id')
        coupon_code = data.get('coupon', '')
        notice_mail = data.get('mail')
        attach = data.get('attach', [])
        payment = data.get('payment', {})
        timestamp = data.get('time')
        sign = data.get('sign')

        # 验证支付信息
        if not isinstance(payment, dict) or 'id' not in payment or 'way' not in payment:
            return JsonResponse({
                'code': 400,
                'msg': '支付信息格式错误'
            })

        # 验证时间戳
        try:
            timestamp_int = int(timestamp)
            if len(str(timestamp_int)) != 13:
                raise ValueError("时间戳长度不正确")
        except (ValueError, TypeError):
            return JsonResponse({
                'code': 400,
                'msg': '时间戳格式错误'
            })

        # 从请求头获取Token
        token = request.headers.get('Token')
        time_header = request.headers.get('time')

        if not token:
            return JsonResponse({
                'code': 401,
                'msg': '缺少Token'
            })

        # 验证JWT Token并获取用户信息
        user_key = None
        user_id = None
        is_guest = False

        # 使用通用验证函数
        auth_result = verify_user_token_and_key(request, require_user_id_match=False)
        if not auth_result['success']:
            return auth_result['error_response']

        user_id = auth_result['user_id']
        user_key = auth_result['user_key']
        is_guest = auth_result['is_guest']

        # 验证签名
        if not sign:
            return JsonResponse({
                'code': 400,
                'msg': '缺少签名'
            })

        # 创建签名验证数据（排除sign字段）
        sign_data = {k: v for k, v in data.items() if k != 'sign'}

        # 将数据转换为JSON字符串，按ASCII码排序
        json_str = json.dumps(sign_data, separators=(',', ':'), sort_keys=True, ensure_ascii=False)

        # 添加用户key
        sign_text = json_str + user_key

        # 计算MD5签名
        expected_sign = hashlib.md5(sign_text.encode('utf-8')).hexdigest()

        if sign != expected_sign:
            return JsonResponse({
                'code': 403,
                'msg': '签名验证失败'
            })

        # 验证商品信息
        try:
            goods = Goods.objects.get(id=product_id)
        except Goods.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'msg': '商品不存在'
            })

        # 检查商品状态
        if goods.status != '1':
            return JsonResponse({
                'code': 400,
                'msg': '商品已下架或售罄'
            })

        # 检查库存
        if goods.stock <= 0:
            return JsonResponse({
                'code': 400,
                'msg': '商品库存不足'
            })

        try:
            payment_method = PaymentMethod.objects.get(id=payment['id'], is_active=True)
        except PaymentMethod.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'msg': '支付渠道不存在或已停用'
            })

        try:
            # 获取用户会员等级key
            if not is_guest:
                user_obj = User.objects.get(user_id=user_id)
                membership_level = user_obj.membership_level

                # 获取会员等级对应的ID作为key
                try:
                    member_level = MemberLevel.objects.get(name=membership_level)
                    user_membership_key = str(member_level.id)
                except MemberLevel.DoesNotExist:
                    user_membership_key = "NormalUser"  # 默认为普通用户
            else:
                user_membership_key = "NormalUser"  # 游客用户默认为普通用户

            # 使用calculate_actual_price函数计算实际价格
            calculated_price_str = calculate_actual_price(goods.price, goods.price_template, user_membership_key)
            base_price = float(calculated_price_str)

        except Exception as e:
            # 价格计算异常，使用原价
            base_price = float(goods.price)

        # 处理优惠券
        discount_amount = 0
        if coupon_code:
            try:
                coupon = Coupon.objects.get(code=coupon_code, is_active=True)

                # 检查优惠券是否过期
                if coupon.expiry_date and timezone.now() > coupon.expiry_date:
                    return JsonResponse({
                        'code': 400,
                        'msg': '优惠券已过期'
                    })

                # 计算折扣
                if coupon.discount_type == 'fixed':  # 固定金额折扣
                    discount_amount = float(coupon.discount_value)
                elif coupon.discount_type == 'percentage':  # 百分比折扣
                    discount_amount = base_price * float(coupon.discount_value) / 100

                # 检查最小消费限制
                if coupon.min_order_amount and base_price < float(coupon.min_order_amount):
                    return JsonResponse({
                        'code': 400,
                        'msg': f'使用此优惠券需要最低消费{coupon.min_order_amount}元'
                    })

            except Coupon.DoesNotExist:
                return JsonResponse({
                    'code': 404,
                    'msg': '优惠券不存在'
                })

        # 计算最终价格
        final_price = max(0, base_price - discount_amount)
        # 生成订单号
        order_id = generate_order_id()

        # 生成支付链接
        payment_result = generate_payment_url(payment_method, payment['way'], order_id, goods.name, final_price, device_type)
        payment_url = None

        if payment_result.get('code') == 200:
            if payment_result.get('type') == 'url':
                payment_url = payment_result.get('data',{}).get('url')
            elif payment_result.get('type') == 'api':
                # API类型的支付，二维码URL将在支付页面中获取
                payment_url = f'/payment/qrcode/?order={order_id}'
        else:
            return JsonResponse({
                'code': 500,
                'msg': '生成支付链接失败'
            })

        # 清理过期的订单（将1小时前的pending订单状态改为cancelled）
        from api.models import Order
        from datetime import timedelta
        cutoff_time = timezone.now() - timedelta(hours=1)
        expired_orders = Order.objects.filter(
            created_at__lt=cutoff_time,
            data__status='pending'
        )
        expired_count = 0
        for expired_order in expired_orders:
            expired_order_data = expired_order.data
            expired_order_data['status'] = 'cancelled'

            # 添加订单取消历史记录
            from api.views import add_order_history
            expired_order_data = add_order_history(
                order_data=expired_order_data,
                status='cancelled',
                action='order_cancelled',
                description='订单超过1小时未支付，自动取消',
                details={
                    'cancel_reason': 'timeout',
                    'timeout_hours': 1,
                    'auto_cancelled': True
                }
            )

            expired_order.data = expired_order_data
            expired_order.save()
            expired_count += 1


        # 创建订单数据，确保所有UUID类型都转为字符串
        order_data = {
            'order_id': str(order_id),
            'product':{
                'id': str(product_id),
                'name': goods.name,
                'price': str(goods.price),
                'type': goods.type
            },
            'user':{
                'id': str(user_id),
                'is_guest': is_guest,
                'notice_mail': notice_mail
            },
            'payment':{
                'id': str(payment_method.id),
                'name': payment_method.name,
                'way': payment['way'],
                'type': payment_result.get('type'),
                'url': payment_url,
                'create_time': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            'price':{
                'original_price': str(base_price),
                'discount_amount': str(discount_amount),
                'final_price': str(final_price),
                'coupon': coupon_code
            },
            'attach': attach,
            'status': 'pending',
            'device_type': device_type
        }

        # 如果是对接商品，添加docking_info字段
        if goods.type == 3:
            from api.models import DockingSite
            try:
                docking_site = DockingSite.objects.get(id=goods.docking_site.id)
                order_data['product']['docking_info'] = {
                    'docking_site_name': docking_site.name,
                    'docking_site_id': str(goods.docking_site.id),
                    'docking_product_id': str(goods.docking_id)
                }
            except (DockingSite.DoesNotExist, AttributeError):
                # 如果对接站点不存在或goods.docking_site为None，设置默认值
                order_data['product']['docking_info'] = {
                    'docking_site_name': '未知对接站点',
                    'docking_site_id': '',
                    'docking_product_id': str(goods.docking_id) if goods.docking_id else ''
                }

        # 添加订单创建历史记录
        from api.views import add_order_history
        order_data = add_order_history(
            order_data=order_data,
            status='pending',
            action='order_created',
            description='订单创建成功',
            details={
                'product_name': goods.name,
                'final_price': str(final_price),
                'payment_method': payment_method.name,
                'coupon_used': coupon_code if coupon_code else None
            },
            request=request
        )

        # 直接将订单数据存储到正式订单表
        try:
            order = Order.objects.create(
                id=order_id,
                order='',  # 初始为空，支付成功后更新为支付平台订单号
                user=user_id,
                is_guest_order=is_guest,
                data=order_data
            )
        except Exception as e:
            return JsonResponse({
                'code': 500,
                'msg': '订单创建失败，请重试' + str(e)
            })

        return JsonResponse({
            'code': 200,
            'msg': '订单创建成功',
            'data': {
                'order_id': order_id,
                'final_price': final_price,
                'payment_url': payment_url,  # 根据支付类型返回相应的URL
                'payment_type': payment_result.get('type'),  # 添加支付类型标识
                'order_status': 'pending'
            }
        })

    except Exception as e:
        return JsonResponse({
            'code': 500,
            'msg': '服务器内部错误'
        })


@csrf_exempt
@require_http_methods(["GET"])
@api_view(['GET'])
@permission_classes([AllowAny])
def check_payment_status_api(request):

    try:
        order_id = request.GET.get('order')

        if not order_id:
            return JsonResponse({
                'code': 400,
                'msg': '订单号不能为空'
            })

        # 从订单表获取订单数据
        try:
            from api.models import Order
            order = Order.objects.get(id=order_id)
            order_data = order.data
        except Order.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'msg': '订单不存在'
            })

        # 获取当前订单状态
        current_status = order_data.get('status', 'pending')

        # 如果订单已经是已支付状态，直接返回
        if current_status == 'paid':
            return JsonResponse({
                'code': 200,
                'msg': '支付状态查询成功',
                'data': {
                    'status': 'paid',
                    'order_id': order_id,
                    'message': '支付成功'
                }
            })
        elif current_status == 'cancelled':
            return JsonResponse({
                'code': 200,
                'msg': '支付状态查询成功',
                'data': {
                    'status': 'cancelled',
                    'order_id': order_id,
                    'message': '订单已取消'
                }
            })
        elif current_status == 'finish':
            return JsonResponse({
                'code': 200,
                'msg': '支付状态查询成功',
                'data': {
                    'status': 'finish',
                    'order_id': order_id,
                    'message': '订单已完成'
                }
            })

        # TODO: 这里应该调用第三方支付平台的查询接口来获取真实的支付状态
        # 目前返回pending状态，实际项目中需要根据支付平台的API来实现

        # 但是易支付submit类型的支付接口没有查询订单接口 只依靠异步通知

        # 检查订单是否超时（超过1小时自动取消）
        from datetime import timedelta
        from django.utils import timezone

        order_created_time = order.created_at
        current_time = timezone.now()
        time_diff = current_time - order_created_time

        if time_diff > timedelta(hours=1) and current_status == 'pending':
            # 订单超时，更新状态为已取消
            order_data['status'] = 'cancelled'

            # 添加订单取消历史记录
            from api.views import add_order_history
            order_data = add_order_history(
                order_data=order_data,
                status='cancelled',
                action='order_cancelled',
                description='订单超时取消（支付状态查询时检测）',
                details={
                    'cancel_reason': 'timeout',
                    'timeout_hours': 1,
                    'auto_cancelled': True,
                    'cancelled_by': 'status_check'
                },
                request=request
            )

            order.data = order_data
            order.save()

            return JsonResponse({
                'code': 200,
                'msg': '支付状态查询成功',
                'data': {
                    'status': 'cancelled',
                    'order_id': order_id,
                    'message': '订单已超时取消'
                }
            })

        # 返回待支付状态
        return JsonResponse({
            'code': 200,
            'msg': '支付状态查询成功',
            'data': {
                'status': 'pending',
                'order_id': order_id,
                'message': '等待支付中'
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'code': 400,
            'msg': '请求数据格式错误'
        })
    except Exception as e:
        return JsonResponse({
            'code': 500,
            'msg': '服务器内部错误'
        })


@csrf_exempt
@require_http_methods(["GET"])
@api_view(['GET'])
@permission_classes([AllowAny])
def get_order_history_api(request):

    try:
        order_id = request.GET.get('order')

        if not order_id:
            return JsonResponse({
                'code': 400,
                'msg': '订单号不能为空'
            })

        # 使用OrderService获取订单详情
        from utils.shared_services.common.auth_service import AuthService
        from utils.shared_services.order.order_service import OrderService
        
        # 创建服务上下文
        context = AuthService.create_context_from_user_request(request)
        
        # 使用订单服务
        order_service = OrderService(context)
        
        try:
            # 获取订单详情
            order_detail = order_service.get_order_detail(order_id, format_type='user')
            
            # 获取历史记录
            detailed_data = order_detail.get('detailed_data', {})
            history = detailed_data.get('history', [])
            
            # 格式化历史记录，添加状态中文描述
            status_map = {
                'pending': '待付款',
                'paid': '已付款',
                'finish': '已完成',
                'cancelled': '已取消',
                '0': '待付款',
                '1': '已付款',
                '2': '已发货',
                '3': '已完成',
                '4': '已取消',
                '5': '已退款'
            }

            action_map = {
                'order_created': '订单创建',
                'payment_success': '支付成功',
                'order_completed': '订单完成',
                'order_cancelled': '订单取消'
            }

            formatted_history = []
            for entry in history:
                formatted_entry = {
                    'timestamp': entry.get('timestamp', ''),
                    'status': entry.get('status', ''),
                    'status_name': status_map.get(entry.get('status', ''), entry.get('status', '')),
                    'action': entry.get('action', ''),
                    'action_name': action_map.get(entry.get('action', ''), entry.get('action', '')),
                    'description': entry.get('description', ''),
                    'details': entry.get('details', {})
                }
                formatted_history.append(formatted_entry)

            return JsonResponse({
                'code': 200,
                'msg': '获取订单历史成功',
                'data': {
                    'order_id': order_id,
                    'current_status': order_detail.get('status', 'pending'),
                    'current_status_name': status_map.get(order_detail.get('status', 'pending'), '未知'),
                    'history_count': len(formatted_history),
                    'history': formatted_history
                }
            })
            
        except Exception as e:
            if 'not found' in str(e).lower() or '不存在' in str(e):
                return JsonResponse({
                    'code': 404,
                    'msg': '订单不存在'
                })
            raise

    except Exception as e:
        return JsonResponse({
            'code': 500,
            'msg': '服务器内部错误'
        })


# 充值余额API
@csrf_exempt
def recharge_balance_api(request):

    if request.method != 'POST':
        return JsonResponse({
            'code': 405,
            'msg': '请求方法不允许'
        })

    try:
        # 解析请求数据
        data = json.loads(request.body)

        # 验证必需参数
        required_fields = ['payMent', 'way', 'userId', 'money', 'sign']
        for field in required_fields:
            if field not in data:
                return JsonResponse({
                    'code': 400,
                    'msg': f'缺少必需参数: {field}'
                })

        payment_id = data['payMent']
        payment_way = data['way']
        user_id = data['userId']
        money = data['money']
        sign = data['sign']

        # 验证充值金额
        try:
            money = float(money)
            if money <= 0:
                return JsonResponse({
                    'code': 400,
                    'msg': '充值金额必须大于0'
                })
        except (ValueError, TypeError):
            return JsonResponse({
                'code': 400,
                'msg': '充值金额格式错误'
            })

        # 验证用户
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'msg': '用户不存在'
            })

        # 验证签名
        sign_data = {k: v for k, v in data.items() if k != 'sign'}
        sorted_keys = sorted(sign_data.keys())
        sign_string = '&'.join([f'{key}={sign_data[key]}' for key in sorted_keys])
        sign_string += user.user_key

        expected_sign = get_md5(sign_string)
        if sign != expected_sign:
            return JsonResponse({
                'code': 401,
                'msg': '签名验证失败'
            })

        # 验证支付方式
        try:
            payment_method = PaymentMethod.objects.get(id=payment_id, is_active=True)
        except PaymentMethod.DoesNotExist:
            return JsonResponse({
                'code': 404,
                'msg': '支付方式不存在或已禁用'
            })

        # 验证支付方式是否支持指定的支付类型
        if payment_way not in payment_method.data:
            return JsonResponse({
                'code': 400,
                'msg': '该支付渠道不支持指定的支付方式'
            })

        # 获取设备类型
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        device_type = 'desktop'
        mobile_keywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone']
        if any(keyword in user_agent for keyword in mobile_keywords):
            device_type = 'mobile'

        # 生成订单号
        order_id = generate_order_id()

        # 生成支付链接
        payment_result = generate_payment_url(payment_method, payment_way, order_id, f'充值余额-{money}元', money, device_type)
        payment_url = None

        if payment_result.get('code') == 200:
            if payment_result.get('type') == 'url':
                payment_url = payment_result.get('data', {}).get('url')
            elif payment_result.get('type') == 'api':
                # API类型的支付，二维码URL将在支付页面中获取
                payment_url = f'/payment/qrcode/?order={order_id}'
        else:
            return JsonResponse({
                'code': 500,
                'msg': '生成支付链接失败'
            })

        # 创建充值订单数据
        order_data = {
            'order_id': order_id,
            'type': 1,  # 充值订单类型
            'user_id': user_id,
            'payment': {
                'id': payment_method.id,
                'name': payment_method.name,
                'way': payment_way,
                'type': payment_result.get('type'),
                'url': payment_url,
                'create_time': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            'price': str(money),
            'status': 'pending',
            'device_type': device_type
        }

        # 保存订单
        order = Order(
            id=order_id,
            user=user_id,
            type=1,  # 设置订单类型为充值订单
            data=order_data
        )
        order.save()

        # 返回支付链接
        return JsonResponse({
            'code': 200,
            'msg': '生成充值订单成功',
            'url': payment_url,
            'type': payment_result.get('type', 'url')
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'code': 400,
            'msg': '请求数据格式错误'
        })
    except Exception as e:
        return JsonResponse({
            'code': 500,
            'msg': '服务器内部错误'
        })

def order_detail_page(request, order_id):

    import logging
    
    # 获取日志记录器
    logger = logging.getLogger(__name__)
    logger = logging.getLogger(__name__)
    
    try:
        # 记录访问日志
        logger.info(f"用户访问订单详情页，订单号: {order_id}, IP: {request.META.get('REMOTE_ADDR', 'unknown')}")
        # 订单状态映射字典
        ORDER_STATUS_MAPPING = {
            "pending": "订单待付款",
            "paid": "订单已付款", 
            "processing": "订单处理中",
            "completed": "订单已完成",
            "refunded": "订单已退款",
            "failed": "订单失败"
        }

        # 支付方式映射字典
        PAYMENT_METHOD_MAPPING = {
            "wxpay": "微信支付",
            "qqpay": "QQ钱包", 
            "alipay": "支付宝"
        }
        
        # 1. 订单数据查询和验证
        if not order_id:
            # 订单号为空
            return smart_render(request, get_template_path('order-notfound.html'), {})
            
        try:
            # 根据订单号查询Order模型
            order = Order.objects.get(id=order_id)
            order_data = order.data
            
            # 验证订单数据是否存在
            if not order_data:
                return smart_render(request, get_template_path('order-notfound.html'), {})
                
        except Order.DoesNotExist:
            # 订单不存在，返回订单未找到页面
            logger.warning(f"订单不存在: {order_id}, IP: {request.META.get('REMOTE_ADDR', 'unknown')}")
            return smart_render(request, get_template_path('order-notfound.html'), {})
        except Exception as e:
            # 数据库查询异常
            context = {
                'error_title': '数据查询失败',
                'error_message': '无法获取订单信息，请稍后重试。',
                'error_icon': 'fa-exclamation-triangle'
            }
            return smart_render(request, get_template_path('product-error.html'), context)
        
        # 2. 订单JSON数据解析
        try:
            # 提取所需的订单字段信息
            product_id = order_data.get('product', {}).get('id', '')
            product_name = order_data.get('product', {}).get('name', '未知商品')
            local_order_id = order_data.get('order_id', order_id)
            
            # 获取订单创建时间
            history = order_data.get('history', [])
            create_time = ''
            if history and len(history) > 0:
                create_time = history[0].get('timestamp', '')
            
            status_raw = order_data.get('status', 'unknown')
            payment_way_raw = order_data.get('payment', {}).get('way', 'unknown')
            final_price = order_data.get('price', {}).get('final_price', '0.00')
            # 获取卡密内容（如果存在）
            # 首先尝试从order.card获取，如果不存在则尝试从其他位置获取
            card_content = None
            if 'order' in order_data and order_data['order']:
                card_content = order_data['order'].get('card', None)
            
            # 如果order.card不存在，尝试从API响应中获取卡密数据
            if not card_content:
                # 检查history中是否有卡密信息
                for history_item in order_history:
                    if history_item.get('action') == 'order_completed' and 'details' in history_item:
                        details = history_item['details']
                        if 'card_data' in details:
                            card_content = details['card_data']
                            break
                        elif 'kmData' in details and details['kmData']:
                            card_content = details['kmData']
                            break
            
            fulfillment_type = '1'  # 默认为直冲商品
            if 'order' in order_data and order_data['order']:
                fulfillment_type = order_data['order'].get('fulfillment_type', '1')

            order_history = order_data.get('history', [])
            
        except (KeyError, TypeError, AttributeError) as e:
            # JSON数据解析异常
            logger.error(f"订单数据解析异常: {str(e)}, 订单号: {order_id}, IP: {request.META.get('REMOTE_ADDR', 'unknown')}")
            context = {
                'error_title': '数据解析失败',
                'error_message': '订单数据格式异常，无法正确解析。',
                'error_icon': 'fa-exclamation-triangle'
            }
            return smart_render(request, get_template_path('product-error.html'), context)
        
        # 3. 状态和支付方式映射
        try:
            # 订单状态映射
            status_display = ORDER_STATUS_MAPPING.get(status_raw, '未知状态')
            
            # 支付方式映射
            payment_method_display = PAYMENT_METHOD_MAPPING.get(payment_way_raw, '未知支付方式')
            
        except Exception as e:
            # 状态映射异常，使用默认值
            status_display = '未知状态'
            payment_method_display = '未知支付方式'
        
        # 4. 商品信息查询
        product_info = ''
        try:
            if product_id:
                # 根据商品ID查询Goods模型
                goods = Goods.objects.get(id=product_id)
                product_info = goods.info if goods.info else ''
        except Goods.DoesNotExist:
            # 商品不存在，使用空字符串
            print(f"商品不存在: {product_id}")
            product_info = ''
        except Exception as e:
            # 商品查询异常，使用空字符串
            print(f"商品查询异常: {str(e)}")
            product_info = ''
        
        # 5. 商品类型判断和模板选择
        template_name = 'user/order-status_card.html'
        
        if fulfillment_type == 1:
            # 直冲商品 - 暂时留空处理逻辑（按照文档要求）
            context = {
                'error_title': '功能开发中',
                'error_message': '直冲商品订单详情页面正在开发中，请稍后再试。',
                'error_icon': 'fa-tools'
            }
            return smart_render(request, get_template_path('product-error.html'), context)
        
        # 6. 组织模板上下文数据
        context = {
            # 基础订单信息
            'order_id': local_order_id,
            'create_time': create_time,
            'status': status_display,
            'status_raw': status_raw,
            'final_price': final_price,
            'fulfillment_type': fulfillment_type,
            
            # 商品信息
            'product_id': product_id,
            'product_name': product_name,
            'product_info': product_info,
            
            # 支付信息
            'payment_method': payment_method_display,
            'payment_method_raw': payment_way_raw,
            
            # 卡密信息（如果存在）
            'card_content': card_content,
            
            # 订单历史记录
            'order_history': order_history,
            
            # 页面标题
            'page_title': f'订单详情',
            
            # 完整的订单数据（供模板使用）
            'order_data': order_data,
            'order': order
        }
        
        # 提取模板文件名（去掉user/前缀）
        template_file = template_name.replace('user/', '')
        return smart_render(request, get_template_path(template_file), context)
        
    except Exception as e:
        # 异常处理逻辑将在后续任务中完善
        context = {
            'error_title': '页面加载失败',
            'error_message': '页面加载时发生错误，请稍后重试。如果问题持续存在，请联系客服。',
            'error_icon': 'fa-exclamation-triangle'
        }
        return smart_render(request, get_template_path('product-error.html'), context)
